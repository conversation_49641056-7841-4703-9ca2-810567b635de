"""
订单表模型

用于记录量化交易系统中的订单成交信息
"""

from sqlalchemy import Column, Integer, String, DateTime, Enum, DECIMAL, Text
from sqlalchemy.sql import func
import enum
from .pending_order import Base


class SideType(str, enum.Enum):
    """成交方向枚举"""
    BUY = "BUY"      # 买入
    SELL = "SELL"    # 卖出


class EffectType(str, enum.Enum):
    """开平仓类型枚举"""
    OPEN = "OPEN"           # 开仓
    CLOSE = "CLOSE"         # 平仓
    CLOSE_TODAY = "CLOSE_TODAY"     # 平今
    CLOSE_YESTERDAY = "CLOSE_YESTERDAY"  # 平昨


class OrderType(str, enum.Enum):
    """订单类型枚举"""
    MARKET = "MARKET"       # 市价单
    LIMIT = "LIMIT"         # 限价单
    STOP = "STOP"           # 止损单
    STOP_LIMIT = "STOP_LIMIT"   # 止损限价单
    IOC = "IOC"             # 立即成交或取消
    FOK = "FOK"             # 全部成交或取消
    GTC = "GTC"             # 撤销前有效
    GTD = "GTD"             # 指定日期前有效


class Order(Base):
    """
    订单表模型
    
    记录量化交易系统中的订单成交信息，包括成交价格、成交量、
    手续费等详细信息。
    """
    
    __tablename__ = "order"
    
    # 主键，自增ID
    id = Column(
        Integer, 
        primary_key=True, 
        autoincrement=True,
        comment="主键，自增ID"
    )
    
    # 订单号，关联原始订单
    order_no = Column(
        String(64), 
        nullable=False, 
        index=True,
        comment="订单号，关联原始订单"
    )
    
    # 成交号，唯一标识每笔成交
    execution_id = Column(
        String(64), 
        nullable=False, 
        unique=True, 
        index=True,
        comment="成交号，唯一标识每笔成交"
    )
    
    # 成交时间
    execution_time = Column(
        DateTime, 
        nullable=False, 
        index=True,
        comment="成交时间"
    )
    
    # 渠道编号
    channel_code = Column(
        String(32), 
        nullable=False, 
        index=True,
        comment="渠道编号"
    )
    
    # 合约代码
    symbol = Column(
        String(32), 
        nullable=False, 
        index=True,
        comment="合约代码"
    )
    
    # 成交方向 (BUY/SELL)
    side = Column(
        Enum(SideType), 
        nullable=False,
        comment="成交方向"
    )
    
    # 成交价格
    execution_price = Column(
        DECIMAL(18, 8), 
        nullable=False,
        comment="成交价格"
    )
    
    # 成交量
    execution_quantity = Column(
        DECIMAL(18, 8), 
        nullable=False,
        comment="成交量"
    )
    
    # 开平仓类型
    effect_type = Column(
        Enum(EffectType), 
        nullable=False,
        comment="开平仓类型"
    )
    
    # 订单类型
    order_type = Column(
        Enum(OrderType), 
        nullable=False,
        comment="订单类型"
    )
    
    # 近端交割日 (yyyyMMdd)
    value_date = Column(
        String(8), 
        nullable=True,
        comment="近端交割日 (yyyyMMdd)"
    )
    
    # 远端交割日 (yyyyMMdd)
    maturity_date = Column(
        String(8), 
        nullable=True,
        comment="远端交割日 (yyyyMMdd)"
    )
    
    # 手续费
    commission = Column(
        DECIMAL(18, 8), 
        nullable=True, 
        default=0.00,
        comment="手续费"
    )
    
    # 记录创建时间
    create_time = Column(
        DateTime, 
        nullable=False, 
        default=func.now(),
        comment="记录创建时间"
    )
    
    # 记录更新时间
    update_time = Column(
        DateTime, 
        nullable=False, 
        default=func.now(), 
        onupdate=func.now(),
        comment="记录更新时间"
    )
    
    # 备注信息
    remark = Column(
        Text, 
        nullable=True,
        comment="备注信息"
    )
    
    def __repr__(self):
        """字符串表示"""
        return (
            f"<Order(id={self.id}, "
            f"order_no='{self.order_no}', "
            f"execution_id='{self.execution_id}', "
            f"symbol='{self.symbol}', "
            f"side='{self.side}', "
            f"execution_price={self.execution_price}, "
            f"execution_quantity={self.execution_quantity})>"
        )
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'order_no': self.order_no,
            'execution_id': self.execution_id,
            'execution_time': self.execution_time.isoformat() if self.execution_time else None,
            'channel_code': self.channel_code,
            'symbol': self.symbol,
            'side': self.side.value if self.side else None,
            'execution_price': float(self.execution_price) if self.execution_price else None,
            'execution_quantity': float(self.execution_quantity) if self.execution_quantity else None,
            'effect_type': self.effect_type.value if self.effect_type else None,
            'order_type': self.order_type.value if self.order_type else None,
            'value_date': self.value_date,
            'maturity_date': self.maturity_date,
            'commission': float(self.commission) if self.commission else None,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None,
            'remark': self.remark
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建实例"""
        from datetime import datetime
        
        # 处理时间字段
        if isinstance(data.get('execution_time'), str):
            data['execution_time'] = datetime.fromisoformat(data['execution_time'])
        if isinstance(data.get('create_time'), str):
            data['create_time'] = datetime.fromisoformat(data['create_time'])
        if isinstance(data.get('update_time'), str):
            data['update_time'] = datetime.fromisoformat(data['update_time'])
        
        # 处理枚举字段
        if isinstance(data.get('side'), str):
            data['side'] = SideType(data['side'])
        if isinstance(data.get('effect_type'), str):
            data['effect_type'] = EffectType(data['effect_type'])
        if isinstance(data.get('order_type'), str):
            data['order_type'] = OrderType(data['order_type'])
        
        return cls(**data)
