"""
持仓表模型

用于记录量化交易系统中的持仓信息，包括持仓状态、数量、价格、盈亏等
"""

from sqlalchemy import Column, Integer, String, DateTime, Enum, DECIMAL, Text, Boolean
from sqlalchemy.sql import func
import enum
from decimal import Decimal
from .pending_order import Base


class PositionSide(str, enum.Enum):
    """持仓方向枚举"""
    NEUTRAL = "NEUTRAL"    # 中性（无持仓）
    LONG = "LONG"          # 多头持仓
    SHORT = "SHORT"        # 空头持仓


class PositionStatus(str, enum.Enum):
    """持仓状态枚举"""
    OPEN = "OPEN"          # 持仓中
    CLOSED = "CLOSED"      # 已平仓
    PARTIAL = "PARTIAL"    # 部分平仓


class PositionType(str, enum.Enum):
    """持仓类型枚举"""
    NORMAL = "NORMAL"      # 普通持仓
    HEDGE = "HEDGE"        # 对冲持仓
    ARBITRAGE = "ARBITRAGE"  # 套利持仓


class Position(Base):
    """
    持仓表模型
    
    记录量化交易系统中的持仓信息，包括持仓状态、数量、价格、
    盈亏计算等详细信息。
    """
    
    __tablename__ = "position"
    
    # 主键，自增ID
    id = Column(
        Integer, 
        primary_key=True, 
        autoincrement=True,
        comment="主键，自增ID"
    )
    
    # 持仓编号，唯一标识每个持仓
    position_id = Column(
        String(64), 
        nullable=False, 
        unique=True, 
        index=True,
        comment="持仓编号，唯一标识"
    )
    
    # 关联的开仓订单号
    open_order_id = Column(
        String(64), 
        nullable=False, 
        index=True,
        comment="开仓订单ID"
    )
    
    # 渠道编号
    channel_code = Column(
        String(32), 
        nullable=False, 
        index=True,
        comment="渠道编号"
    )
    
    # 合约代码
    symbol = Column(
        String(32), 
        nullable=False, 
        index=True,
        comment="合约代码"
    )
    
    # 持仓方向
    position_side = Column(
        Enum(PositionSide), 
        nullable=False,
        index=True,
        comment="持仓方向"
    )
    
    # 持仓状态
    position_status = Column(
        Enum(PositionStatus), 
        nullable=False,
        default=PositionStatus.OPEN,
        index=True,
        comment="持仓状态"
    )
    
    # 持仓类型
    position_type = Column(
        Enum(PositionType), 
        nullable=False,
        default=PositionType.NORMAL,
        comment="持仓类型"
    )
    
    # 开仓时间
    open_time = Column(
        DateTime, 
        nullable=False, 
        index=True,
        comment="开仓时间"
    )
    
    # 开仓价格
    open_price = Column(
        DECIMAL(18, 8), 
        nullable=False,
        comment="开仓价格"
    )
    
    # 当前价格
    current_price = Column(
        DECIMAL(18, 8), 
        nullable=True,
        comment="当前价格"
    )
    
    # 总持仓量
    total_quantity = Column(
        DECIMAL(18, 8), 
        nullable=False,
        comment="总持仓量"
    )
    
    # 可用持仓量（总持仓量 - 冻结量）
    available_quantity = Column(
        DECIMAL(18, 8), 
        nullable=False,
        comment="可用持仓量"
    )
    
    # 冻结持仓量
    frozen_quantity = Column(
        DECIMAL(18, 8), 
        nullable=False,
        default=0.00,
        comment="冻结持仓量"
    )
    
    # 今日持仓量
    today_quantity = Column(
        DECIMAL(18, 8), 
        nullable=False,
        default=0.00,
        comment="今日持仓量"
    )
    
    # 持仓成本
    position_cost = Column(
        DECIMAL(18, 8), 
        nullable=False,
        comment="持仓成本"
    )
    
    # 持仓市值
    market_value = Column(
        DECIMAL(18, 8), 
        nullable=True,
        comment="持仓市值"
    )
    
    # 未实现盈亏
    unrealized_pnl = Column(
        DECIMAL(18, 8), 
        nullable=True,
        default=0.00,
        comment="未实现盈亏"
    )
    
    # 已实现盈亏
    realized_pnl = Column(
        DECIMAL(18, 8), 
        nullable=False,
        default=0.00,
        comment="已实现盈亏"
    )
    
    # 总盈亏
    total_pnl = Column(
        DECIMAL(18, 8), 
        nullable=True,
        default=0.00,
        comment="总盈亏"
    )
    
    # 手续费
    commission = Column(
        DECIMAL(18, 8), 
        nullable=False,
        default=0.00,
        comment="手续费"
    )
    
    # 隔夜利息
    swap_fee = Column(
        DECIMAL(18, 8), 
        nullable=False,
        default=0.00,
        comment="隔夜利息"
    )
    
    # 止损价格
    stop_loss_price = Column(
        DECIMAL(18, 8), 
        nullable=True,
        comment="止损价格"
    )
    
    # 止盈价格
    take_profit_price = Column(
        DECIMAL(18, 8), 
        nullable=True,
        comment="止盈价格"
    )
    
    # 平仓时间
    close_time = Column(
        DateTime, 
        nullable=True,
        comment="平仓时间"
    )
    
    # 平仓价格
    close_price = Column(
        DECIMAL(18, 8), 
        nullable=True,
        comment="平仓价格"
    )
    
    # 平仓订单ID
    close_order_id = Column(
        String(64), 
        nullable=True,
        comment="平仓订单ID"
    )
    
    # 是否活跃持仓
    is_active = Column(
        Boolean, 
        nullable=False,
        default=True,
        index=True,
        comment="是否活跃持仓"
    )
    
    # 记录创建时间
    create_time = Column(
        DateTime, 
        nullable=False, 
        default=func.now(),
        comment="记录创建时间"
    )
    
    # 记录更新时间
    update_time = Column(
        DateTime, 
        nullable=False, 
        default=func.now(), 
        onupdate=func.now(),
        comment="记录更新时间"
    )
    
    # 备注信息
    remark = Column(
        Text, 
        nullable=True,
        comment="备注信息"
    )
    
    def __repr__(self):
        """字符串表示"""
        return (
            f"<Position(id={self.id}, "
            f"position_id='{self.position_id}', "
            f"symbol='{self.symbol}', "
            f"side='{self.position_side}', "
            f"quantity={self.total_quantity}, "
            f"open_price={self.open_price}, "
            f"unrealized_pnl={self.unrealized_pnl})>"
        )
    
    def calculate_unrealized_pnl(self, current_price: Decimal = None) -> Decimal:
        """
        计算未实现盈亏
        
        Args:
            current_price: 当前价格，如果不提供则使用模型中的current_price
            
        Returns:
            未实现盈亏
        """
        if current_price is None:
            current_price = self.current_price
            
        if current_price is None or self.open_price is None:
            return Decimal('0.00')
        
        price_diff = current_price - self.open_price
        
        # 多头持仓：当前价格 - 开仓价格
        # 空头持仓：开仓价格 - 当前价格
        if self.position_side == PositionSide.LONG:
            pnl = price_diff * self.total_quantity
        elif self.position_side == PositionSide.SHORT:
            pnl = -price_diff * self.total_quantity
        else:
            pnl = Decimal('0.00')
        
        return pnl
    
    def calculate_total_pnl(self, current_price: Decimal = None) -> Decimal:
        """
        计算总盈亏（已实现 + 未实现 - 手续费 - 隔夜利息）
        
        Args:
            current_price: 当前价格
            
        Returns:
            总盈亏
        """
        unrealized = self.calculate_unrealized_pnl(current_price)
        total = self.realized_pnl + unrealized - self.commission - self.swap_fee
        return total
    
    def update_current_price(self, current_price: Decimal):
        """
        更新当前价格并重新计算盈亏
        
        Args:
            current_price: 当前价格
        """
        self.current_price = current_price
        self.unrealized_pnl = self.calculate_unrealized_pnl(current_price)
        self.total_pnl = self.calculate_total_pnl(current_price)
        
        # 计算市值
        if current_price:
            self.market_value = current_price * self.total_quantity

    def is_profitable(self, current_price: Decimal = None) -> bool:
        """
        判断持仓是否盈利

        Args:
            current_price: 当前价格

        Returns:
            是否盈利
        """
        total_pnl = self.calculate_total_pnl(current_price)
        return total_pnl > 0

    def get_return_rate(self, current_price: Decimal = None) -> Decimal:
        """
        计算持仓收益率

        Args:
            current_price: 当前价格

        Returns:
            收益率（百分比）
        """
        if self.position_cost == 0:
            return Decimal('0.00')

        total_pnl = self.calculate_total_pnl(current_price)
        return (total_pnl / self.position_cost) * 100

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'position_id': self.position_id,
            'open_order_id': self.open_order_id,
            'channel_code': self.channel_code,
            'symbol': self.symbol,
            'position_side': self.position_side.value if self.position_side else None,
            'position_status': self.position_status.value if self.position_status else None,
            'position_type': self.position_type.value if self.position_type else None,
            'open_time': self.open_time.isoformat() if self.open_time else None,
            'open_price': float(self.open_price) if self.open_price else None,
            'current_price': float(self.current_price) if self.current_price else None,
            'total_quantity': float(self.total_quantity) if self.total_quantity else None,
            'available_quantity': float(self.available_quantity) if self.available_quantity else None,
            'frozen_quantity': float(self.frozen_quantity) if self.frozen_quantity else None,
            'today_quantity': float(self.today_quantity) if self.today_quantity else None,
            'position_cost': float(self.position_cost) if self.position_cost else None,
            'market_value': float(self.market_value) if self.market_value else None,
            'unrealized_pnl': float(self.unrealized_pnl) if self.unrealized_pnl else None,
            'realized_pnl': float(self.realized_pnl) if self.realized_pnl else None,
            'total_pnl': float(self.total_pnl) if self.total_pnl else None,
            'commission': float(self.commission) if self.commission else None,
            'swap_fee': float(self.swap_fee) if self.swap_fee else None,
            'stop_loss_price': float(self.stop_loss_price) if self.stop_loss_price else None,
            'take_profit_price': float(self.take_profit_price) if self.take_profit_price else None,
            'close_time': self.close_time.isoformat() if self.close_time else None,
            'close_price': float(self.close_price) if self.close_price else None,
            'close_order_id': self.close_order_id,
            'is_active': self.is_active,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None,
            'remark': self.remark
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建实例"""
        from datetime import datetime

        # 处理时间字段
        if isinstance(data.get('open_time'), str):
            data['open_time'] = datetime.fromisoformat(data['open_time'])
        if isinstance(data.get('close_time'), str):
            data['close_time'] = datetime.fromisoformat(data['close_time'])
        if isinstance(data.get('create_time'), str):
            data['create_time'] = datetime.fromisoformat(data['create_time'])
        if isinstance(data.get('update_time'), str):
            data['update_time'] = datetime.fromisoformat(data['update_time'])

        # 处理枚举字段
        if isinstance(data.get('position_side'), str):
            data['position_side'] = PositionSide(data['position_side'])
        if isinstance(data.get('position_status'), str):
            data['position_status'] = PositionStatus(data['position_status'])
        if isinstance(data.get('position_type'), str):
            data['position_type'] = PositionType(data['position_type'])

        return cls(**data)
