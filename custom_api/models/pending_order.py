from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

from sqlalchemy import Column, Integer, String, Float, DateTime, Enum
from sqlalchemy.sql import func
import enum


# 订单类型枚举（你可以根据实际业务补充）
class OrderType(str, enum.Enum):
    BUY_LIMIT = "buy_limit"  # 限价买
    SELL_LIMIT = "sell_limit"  # 限价卖
    BUY_STOP = "buy_stop"  # 止损买
    SELL_STOP = "sell_stop"  # 止损卖


# 订单方向枚举
class OrderDirection(str, enum.Enum):
    BUY = "buy"  # 做多
    SELL = "sell"  # 做空


# 开平仓类型枚举
class OpenCloseType(str, enum.Enum):
    OPEN = "open"  # 开仓
    CLOSE = "close"  # 平仓


# 订单状态枚举
class OrderStatus(str, enum.Enum):
    PENDING = "pending"  # 挂单中
    TRIGGERED = "triggered"  # 已触发
    CANCELLED = "cancelled"  # 已取消
    EXPIRED = "expired"  # 已过期


class PendingOrder(Base):
    __tablename__ = "pending_orders"

    id = Column(Integer, primary_key=True, autoincrement=True)  # 内部自增ID
    order_no = Column(String(50), unique=True, nullable=False, index=True)  # 1. 订单号
    order_time = Column(DateTime, default=func.now(), nullable=False)  # 2. 挂单时间
    channel_id = Column(String(20), nullable=False)  # 3. 渠道编号
    symbol = Column(String(20), nullable=False)  # 4. 合约
    order_type = Column(Enum(OrderType), nullable=False)  # 5. 订单类型
    time_in_force = Column(String(20), nullable=True)  # 6. 时效（如GTC/FOK/IOC）
    order_price = Column(Float, nullable=False)  # 7. 挂单价格
    side = Column(Enum(OrderDirection), nullable=False)  # 8. 挂单方向
    quantity = Column(Float, nullable=False)  # 9. 挂单量
    open_close_type = Column(Enum(OpenCloseType), nullable=False)  # 10. 开平仓类型
    stop_loss_price = Column(Float, nullable=True)  # 11. 止损平仓价
    take_profit_price = Column(Float, nullable=True)  # 12. 止盈平仓价
    filled_volume = Column(Float, default=0)  # 13. 成交量
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING)  # 14. 订单状态
    create_time = Column(DateTime, default=func.now())  # 创建时间
    update_time = Column(DateTime, onupdate=func.now())  # 更新时间
    order_params = Column(String(5000), nullable=True)  # 挂单参数
