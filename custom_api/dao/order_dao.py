"""
订单数据访问对象

提供订单表的数据访问操作
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func, between

from .base_dao import BaseDAO
from custom_api.models.order import Order, SideType, EffectType, OrderType
from custom_api.database.connection import get_db_session

logger = logging.getLogger(__name__)


class OrderDAO(BaseDAO[Order, Dict[str, Any], Dict[str, Any]]):
    """订单数据访问对象"""
    
    def __init__(self):
        super().__init__(Order)
    
    def get_by_order_no(self, order_no: str, session: Optional[Session] = None) -> List[Order]:
        """
        根据订单号获取订单列表
        
        Args:
            order_no: 订单号
            session: 数据库会话
            
        Returns:
            订单列表
        """
        def _get_by_order_no(db_session: Session) -> List[Order]:
            try:
                result = db_session.query(Order).filter(Order.order_no == order_no).all()
                logger.debug(f"根据订单号查询订单: {order_no}, 结果数量={len(result)}")
                return result
            except Exception as e:
                logger.error(f"根据订单号查询订单失败: {e}")
                raise
        
        if session:
            return _get_by_order_no(session)
        else:
            with get_db_session() as db_session:
                return _get_by_order_no(db_session)
    
    def get_by_execution_id(self, execution_id: str, session: Optional[Session] = None) -> Optional[Order]:
        """
        根据成交号获取订单
        
        Args:
            execution_id: 成交号
            session: 数据库会话
            
        Returns:
            订单实例或None
        """
        return self.get_by_field('execution_id', execution_id, session)
    
    def get_by_symbol(self, symbol: str, skip: int = 0, limit: int = 100,
                      session: Optional[Session] = None) -> List[Order]:
        """
        根据合约代码获取订单列表
        
        Args:
            symbol: 合约代码
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        return self.filter_by({'symbol': symbol}, skip, limit, 'execution_time', True, session)
    
    def get_by_channel(self, channel_code: str, skip: int = 0, limit: int = 100,
                       session: Optional[Session] = None) -> List[Order]:
        """
        根据渠道编号获取订单列表
        
        Args:
            channel_code: 渠道编号
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        return self.filter_by({'channel_code': channel_code}, skip, limit, 'execution_time', True, session)
    
    def get_by_side(self, side: SideType, skip: int = 0, limit: int = 100,
                    session: Optional[Session] = None) -> List[Order]:
        """
        根据成交方向获取订单列表
        
        Args:
            side: 成交方向
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        return self.filter_by({'side': side}, skip, limit, 'execution_time', True, session)
    
    def get_by_date_range(self, start_date: datetime, end_date: datetime,
                          skip: int = 0, limit: int = 100,
                          session: Optional[Session] = None) -> List[Order]:
        """
        根据日期范围获取订单列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        def _get_by_date_range(db_session: Session) -> List[Order]:
            try:
                result = (db_session.query(Order)
                         .filter(between(Order.execution_time, start_date, end_date))
                         .order_by(desc(Order.execution_time))
                         .offset(skip)
                         .limit(limit)
                         .all())
                logger.debug(f"根据日期范围查询订单: {start_date} - {end_date}, 结果数量={len(result)}")
                return result
            except Exception as e:
                logger.error(f"根据日期范围查询订单失败: {e}")
                raise
        
        if session:
            return _get_by_date_range(session)
        else:
            with get_db_session() as db_session:
                return _get_by_date_range(db_session)
    
    def get_by_price_range(self, min_price: Decimal, max_price: Decimal,
                           skip: int = 0, limit: int = 100,
                           session: Optional[Session] = None) -> List[Order]:
        """
        根据价格范围获取订单列表
        
        Args:
            min_price: 最低价格
            max_price: 最高价格
            skip: 跳过记录数
            limit: 限制记录数
            session: 数据库会话
            
        Returns:
            订单列表
        """
        def _get_by_price_range(db_session: Session) -> List[Order]:
            try:
                result = (db_session.query(Order)
                         .filter(between(Order.execution_price, min_price, max_price))
                         .order_by(desc(Order.execution_time))
                         .offset(skip)
                         .limit(limit)
                         .all())
                logger.debug(f"根据价格范围查询订单: {min_price} - {max_price}, 结果数量={len(result)}")
                return result
            except Exception as e:
                logger.error(f"根据价格范围查询订单失败: {e}")
                raise
        
        if session:
            return _get_by_price_range(session)
        else:
            with get_db_session() as db_session:
                return _get_by_price_range(db_session)
    
    def get_statistics_by_symbol(self, symbol: str, session: Optional[Session] = None) -> Dict[str, Any]:
        """
        获取指定合约的统计信息
        
        Args:
            symbol: 合约代码
            session: 数据库会话
            
        Returns:
            统计信息字典
        """
        def _get_statistics(db_session: Session) -> Dict[str, Any]:
            try:
                # 基础统计
                total_count = db_session.query(func.count(Order.id)).filter(Order.symbol == symbol).scalar()
                
                if total_count == 0:
                    return {
                        'symbol': symbol,
                        'total_count': 0,
                        'buy_count': 0,
                        'sell_count': 0,
                        'total_volume': 0,
                        'total_commission': 0,
                        'avg_price': 0,
                        'min_price': 0,
                        'max_price': 0
                    }
                
                # 买卖统计
                buy_count = db_session.query(func.count(Order.id)).filter(
                    and_(Order.symbol == symbol, Order.side == SideType.BUY)
                ).scalar()
                sell_count = total_count - buy_count
                
                # 成交量和手续费统计
                volume_sum = db_session.query(func.sum(Order.execution_quantity)).filter(
                    Order.symbol == symbol
                ).scalar() or 0
                
                commission_sum = db_session.query(func.sum(Order.commission)).filter(
                    Order.symbol == symbol
                ).scalar() or 0
                
                # 价格统计
                price_stats = db_session.query(
                    func.avg(Order.execution_price),
                    func.min(Order.execution_price),
                    func.max(Order.execution_price)
                ).filter(Order.symbol == symbol).first()
                
                avg_price, min_price, max_price = price_stats
                
                result = {
                    'symbol': symbol,
                    'total_count': total_count,
                    'buy_count': buy_count,
                    'sell_count': sell_count,
                    'total_volume': float(volume_sum),
                    'total_commission': float(commission_sum),
                    'avg_price': float(avg_price) if avg_price else 0,
                    'min_price': float(min_price) if min_price else 0,
                    'max_price': float(max_price) if max_price else 0
                }
                
                logger.debug(f"获取合约统计信息: {symbol}, 结果={result}")
                return result
                
            except Exception as e:
                logger.error(f"获取合约统计信息失败: {e}")
                raise
        
        if session:
            return _get_statistics(session)
        else:
            with get_db_session() as db_session:
                return _get_statistics(db_session)
    
    def search_orders(self, filters: Dict[str, Any], skip: int = 0, limit: int = 100,
                      order_by: str = 'execution_time', desc_order: bool = True,
                      session: Optional[Session] = None) -> List[Order]:
        """
        复合条件搜索订单
        
        Args:
            filters: 搜索条件字典，支持的键：
                - symbol: 合约代码
                - side: 成交方向
                - effect_type: 开平仓类型
                - order_type: 订单类型
                - channel_code: 渠道编号
                - start_date: 开始日期
                - end_date: 结束日期
                - min_price: 最低价格
                - max_price: 最高价格
                - min_quantity: 最小数量
                - max_quantity: 最大数量
            skip: 跳过记录数
            limit: 限制记录数
            order_by: 排序字段
            desc_order: 是否降序
            session: 数据库会话
            
        Returns:
            订单列表
        """
        def _search_orders(db_session: Session) -> List[Order]:
            try:
                query = db_session.query(Order)
                
                # 应用过滤条件
                if 'symbol' in filters:
                    query = query.filter(Order.symbol == filters['symbol'])
                
                if 'side' in filters:
                    query = query.filter(Order.side == filters['side'])
                
                if 'effect_type' in filters:
                    query = query.filter(Order.effect_type == filters['effect_type'])
                
                if 'order_type' in filters:
                    query = query.filter(Order.order_type == filters['order_type'])
                
                if 'channel_code' in filters:
                    query = query.filter(Order.channel_code == filters['channel_code'])
                
                if 'start_date' in filters:
                    query = query.filter(Order.execution_time >= filters['start_date'])
                
                if 'end_date' in filters:
                    query = query.filter(Order.execution_time <= filters['end_date'])
                
                if 'min_price' in filters:
                    query = query.filter(Order.execution_price >= filters['min_price'])
                
                if 'max_price' in filters:
                    query = query.filter(Order.execution_price <= filters['max_price'])
                
                if 'min_quantity' in filters:
                    query = query.filter(Order.execution_quantity >= filters['min_quantity'])
                
                if 'max_quantity' in filters:
                    query = query.filter(Order.execution_quantity <= filters['max_quantity'])
                
                # 应用排序
                if hasattr(Order, order_by):
                    order_field = getattr(Order, order_by)
                    if desc_order:
                        query = query.order_by(desc(order_field))
                    else:
                        query = query.order_by(asc(order_field))
                
                # 应用分页
                result = query.offset(skip).limit(limit).all()
                
                logger.debug(f"复合条件搜索订单: 条件={filters}, 结果数量={len(result)}")
                return result
                
            except Exception as e:
                logger.error(f"复合条件搜索订单失败: {e}")
                raise
        
        if session:
            return _search_orders(session)
        else:
            with get_db_session() as db_session:
                return _search_orders(db_session)


# 全局订单DAO实例
order_dao = OrderDAO()


def get_order_dao() -> OrderDAO:
    """获取订单DAO实例"""
    return order_dao
