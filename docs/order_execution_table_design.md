# 量化交易系统订单成交表设计文档

## 1. 概述

本文档定义了量化交易系统中订单成交表（order_executions）的完整数据库设计，用于记录所有订单的成交信息。该表与现有的 `pending_orders` 表形成完整的订单生命周期管理体系。

## 2. 表结构定义

### 2.1 基本信息

- **表名**: `order`
- **用途**: 存储订单成交记录
- **引擎**: InnoDB (MySQL) / 默认 (SQLite/PostgreSQL)
- **字符集**: utf8mb4 (MySQL)

### 2.2 字段定义

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| `id` | INTEGER | - | NOT NULL | AUTO_INCREMENT | 主键，自增ID |
| `order_no` | VARCHAR | 64 | NOT NULL | - | 订单号，关联原始订单 |
| `execution_id` | VARCHAR | 64 | NOT NULL | - | 成交号，唯一标识每笔成交 |
| `execution_time` | DATETIME | - | NOT NULL | - | 成交时间 |
| `channel_code` | VARCHAR | 32 | NOT NULL | - | 渠道编号 |
| `symbol` | VARCHAR | 32 | NOT NULL | - | 合约代码 |
| `side` | ENUM | - | NOT NULL | - | 成交方向 (BUY/SELL) |
| `execution_price` | DECIMAL | 18,8 | NOT NULL | - | 成交价格 |
| `execution_quantity` | DECIMAL | 18,8 | NOT NULL | - | 成交量 |
| `effect_type` | ENUM | - | NOT NULL | - | 开平仓类型 |
| `order_type` | ENUM | - | NOT NULL | - | 订单类型 |
| `value_date` | VARCHAR | 8 | NULL | - | 近端交割日 (yyyyMMdd) |
| `maturity_date` | VARCHAR | 8 | NULL | - | 远端交割日 (yyyyMMdd) |
| `commission` | DECIMAL | 18,8 | NULL | 0.00 | 手续费 |
| `create_time` | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录创建时间 |
| `update_time` | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录更新时间 |
| `remark` | TEXT | - | NULL | - | 备注信息 |

### 2.3 枚举类型定义

#### 成交方向 (ExecutionSide)
```python
class ExecutionSide(str, enum.Enum):
    BUY = "BUY"      # 买入
    SELL = "SELL"    # 卖出
```

#### 开平仓类型 (EffectType)
```python
class EffectType(str, enum.Enum):
    OPEN = "OPEN"           # 开仓
    CLOSE = "CLOSE"         # 平仓
    CLOSE_TODAY = "CLOSE_TODAY"     # 平今
    CLOSE_YESTERDAY = "CLOSE_YESTERDAY"  # 平昨
```

#### 订单类型 (OrderType)
```python
class OrderType(int, enum.Enum):
    MARKET = 0          # 市价单
    LIMIT = 2           # 限价单
    STOP = 54           # 止损单
    STOP_LIMIT = 52     # 止损限价单
    SOR = 15            # SOR单
```

#### 市场类型 (MarketType)
```python
class MarketType(str, enum.Enum):
    INTERNAL = "INTERNAL"     # 内部市场
    EXTERNAL = "EXTERNAL"     # 外部市场
    BOTH = "BOTH"            # 内外部市场
```

## 3. 主键和索引设计

### 3.1 主键
- **主键**: `id` (自增整数)

### 3.2 唯一索引
- `uk_execution_id`: 成交号唯一索引
  ```sql
  UNIQUE KEY uk_execution_id (execution_id)
  ```

### 3.3 普通索引
- `idx_order_no`: 订单号索引
  ```sql
  INDEX idx_order_no (order_no)
  ```

- `idx_symbol_time`: 合约+成交时间复合索引
  ```sql
  INDEX idx_symbol_time (symbol, execution_time)
  ```

- `idx_channel_time`: 渠道+成交时间复合索引
  ```sql
  INDEX idx_channel_time (channel_code, execution_time)
  ```

- `idx_execution_time`: 成交时间索引
  ```sql
  INDEX idx_execution_time (execution_time)
  ```

- `idx_side_symbol`: 方向+合约复合索引
  ```sql
  INDEX idx_side_symbol (side, symbol)
  ```

- `idx_create_time`: 创建时间索引
  ```sql
  INDEX idx_create_time (create_time)
  ```

## 4. 字段说明和业务含义

### 4.1 核心业务字段

| 字段 | 业务含义 | 数据来源 |
|------|----------|----------|
| `order_no` | 原始订单号，关联pending_orders表 | 订单系统生成 |
| `execution_id` | 成交唯一标识，全局唯一 | 交易所/柜台系统 |
| `execution_time` | 实际成交发生时间 | 交易所时间戳 |
| `channel_code` | 交易渠道标识 | 系统配置 |
| `symbol` | 交易标的合约代码 | 标准化合约代码 |
| `side` | 买卖方向 | 订单执行结果 |
| `execution_price` | 实际成交价格 | 市场撮合结果 |
| `execution_quantity` | 实际成交数量 | 市场撮合结果 |

### 4.2 交割相关字段

| 字段 | 业务含义 | 适用场景 |
|------|----------|----------|
| `value_date` | 近端交割日 | 外汇掉期、债券交易 |
| `maturity_date` | 远端交割日 | 外汇掉期、期货交易 |
| `settlement_date` | 资金结算日期 | 所有交易类型 |

### 4.3 费用和金额字段

| 字段 | 业务含义 | 计算方式 |
|------|----------|----------|
| `commission` | 交易手续费 | 按费率计算或固定收费 |
| `execution_amount` | 成交金额 | price × quantity |
| `currency` | 计价货币 | 合约规格定义 |

## 5. 数据约束和验证规则

### 5.1 检查约束

```sql
-- 成交价格必须为正数
CONSTRAINT check_positive_execution_price 
CHECK (execution_price > 0)

-- 成交量必须为正数
CONSTRAINT check_positive_execution_quantity 
CHECK (execution_quantity > 0)

-- 手续费不能为负数
CONSTRAINT check_non_negative_commission 
CHECK (commission >= 0)

-- 版本号必须为正数
CONSTRAINT check_positive_version 
CHECK (version > 0)

-- 日期格式验证（近端交割日）
CONSTRAINT check_value_date_format 
CHECK (value_date IS NULL OR value_date REGEXP '^[0-9]{8}$')

-- 日期格式验证（远端交割日）
CONSTRAINT check_maturity_date_format 
CHECK (maturity_date IS NULL OR maturity_date REGEXP '^[0-9]{8}$')
```

### 5.2 业务规则约束

```sql
-- 远端交割日必须大于等于近端交割日
CONSTRAINT check_maturity_after_value 
CHECK (maturity_date IS NULL OR value_date IS NULL OR maturity_date >= value_date)

-- 成交时间不能早于创建时间
CONSTRAINT check_execution_after_create 
CHECK (execution_time >= create_time)
```

## 6. 与其他表的关联关系

### 6.1 与pending_orders表的关系

```sql
-- 外键关联（可选，根据业务需求决定是否添加）
FOREIGN KEY (order_no) REFERENCES pending_orders(order_id)
ON DELETE RESTRICT ON UPDATE CASCADE
```

**关联说明**:
- 一个订单可以有多次成交（部分成交场景）
- 通过order_no字段关联原始订单信息
- 支持订单的完整生命周期追踪

### 6.2 关联查询示例

```sql
-- 查询订单及其所有成交记录
SELECT 
    po.order_id,
    po.symbol,
    po.side,
    po.quantity as order_quantity,
    oe.execution_id,
    oe.execution_time,
    oe.execution_price,
    oe.execution_quantity
FROM pending_orders po
LEFT JOIN order_executions oe ON po.order_id = oe.order_no
WHERE po.symbol = 'EURUSD'
ORDER BY oe.execution_time DESC;
```

## 7. 建表SQL语句

### 7.1 SQLite版本

```sql
CREATE TABLE order_executions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_no VARCHAR(64) NOT NULL,
    execution_id VARCHAR(64) NOT NULL UNIQUE,
    execution_time DATETIME NOT NULL,
    channel_code VARCHAR(32) NOT NULL,
    symbol VARCHAR(32) NOT NULL,
    side VARCHAR(10) NOT NULL CHECK (side IN ('BUY', 'SELL')),
    execution_price DECIMAL(18,8) NOT NULL CHECK (execution_price > 0),
    execution_quantity DECIMAL(18,8) NOT NULL CHECK (execution_quantity > 0),
    effect_type VARCHAR(20) NOT NULL CHECK (effect_type IN ('OPEN', 'CLOSE', 'CLOSE_TODAY', 'CLOSE_YESTERDAY')),
    order_type INTEGER NOT NULL,
    value_date VARCHAR(8),
    maturity_date VARCHAR(8),
    commission DECIMAL(18,8) DEFAULT 0.00 CHECK (commission >= 0),
    execution_amount DECIMAL(18,2),
    currency VARCHAR(8),
    counterparty_id VARCHAR(64),
    counterparty_name VARCHAR(128),
    trader_id VARCHAR(64),
    trader_name VARCHAR(128),
    external_execution_id VARCHAR(64),
    settlement_date DATE,
    market_type VARCHAR(20) NOT NULL DEFAULT 'EXTERNAL' CHECK (market_type IN ('INTERNAL', 'EXTERNAL', 'BOTH')),
    execution_venue VARCHAR(64),
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    version INTEGER NOT NULL DEFAULT 1 CHECK (version > 0),
    remark TEXT,
    
    -- 约束检查
    CHECK (maturity_date IS NULL OR value_date IS NULL OR maturity_date >= value_date),
    CHECK (execution_time >= create_time)
);

-- 创建索引
CREATE UNIQUE INDEX uk_execution_id ON order_executions(execution_id);
CREATE INDEX idx_order_no ON order_executions(order_no);
CREATE INDEX idx_symbol_time ON order_executions(symbol, execution_time);
CREATE INDEX idx_channel_time ON order_executions(channel_code, execution_time);
CREATE INDEX idx_execution_time ON order_executions(execution_time);
CREATE INDEX idx_side_symbol ON order_executions(side, symbol);
CREATE INDEX idx_create_time ON order_executions(create_time);

-- 创建更新时间触发器
CREATE TRIGGER update_order_executions_timestamp
    AFTER UPDATE ON order_executions
    FOR EACH ROW
BEGIN
    UPDATE order_executions
    SET update_time = CURRENT_TIMESTAMP
    WHERE id = NEW.id;
END;
```

### 7.2 MySQL版本

```sql
CREATE TABLE order_executions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    order_no VARCHAR(64) NOT NULL COMMENT '订单号',
    execution_id VARCHAR(64) NOT NULL UNIQUE COMMENT '成交号',
    execution_time DATETIME NOT NULL COMMENT '成交时间',
    channel_code VARCHAR(32) NOT NULL COMMENT '渠道编号',
    symbol VARCHAR(32) NOT NULL COMMENT '合约代码',
    side ENUM('BUY', 'SELL') NOT NULL COMMENT '成交方向',
    execution_price DECIMAL(18,8) NOT NULL COMMENT '成交价格',
    execution_quantity DECIMAL(18,8) NOT NULL COMMENT '成交量',
    effect_type ENUM('OPEN', 'CLOSE', 'CLOSE_TODAY', 'CLOSE_YESTERDAY') NOT NULL COMMENT '开平仓类型',
    order_type INT NOT NULL COMMENT '订单类型',
    value_date VARCHAR(8) NULL COMMENT '近端交割日',
    maturity_date VARCHAR(8) NULL COMMENT '远端交割日',
    commission DECIMAL(18,8) DEFAULT 0.00 COMMENT '手续费',
    execution_amount DECIMAL(18,2) NULL COMMENT '成交金额',
    currency VARCHAR(8) NULL COMMENT '计价货币',
    counterparty_id VARCHAR(64) NULL COMMENT '对手方ID',
    counterparty_name VARCHAR(128) NULL COMMENT '对手方名称',
    trader_id VARCHAR(64) NULL COMMENT '交易员ID',
    trader_name VARCHAR(128) NULL COMMENT '交易员姓名',
    external_execution_id VARCHAR(64) NULL COMMENT '外部系统成交ID',
    settlement_date DATE NULL COMMENT '结算日期',
    market_type ENUM('INTERNAL', 'EXTERNAL', 'BOTH') NOT NULL DEFAULT 'EXTERNAL' COMMENT '执行市场类型',
    execution_venue VARCHAR(64) NULL COMMENT '成交场所',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(64) NULL COMMENT '创建者',
    updated_by VARCHAR(64) NULL COMMENT '更新者',
    version INT NOT NULL DEFAULT 1 COMMENT '版本号',
    remark TEXT NULL COMMENT '备注信息',

    -- 约束检查
    CONSTRAINT check_positive_execution_price CHECK (execution_price > 0),
    CONSTRAINT check_positive_execution_quantity CHECK (execution_quantity > 0),
    CONSTRAINT check_non_negative_commission CHECK (commission >= 0),
    CONSTRAINT check_positive_version CHECK (version > 0),
    CONSTRAINT check_value_date_format CHECK (value_date IS NULL OR value_date REGEXP '^[0-9]{8}$'),
    CONSTRAINT check_maturity_date_format CHECK (maturity_date IS NULL OR maturity_date REGEXP '^[0-9]{8}$'),
    CONSTRAINT check_maturity_after_value CHECK (maturity_date IS NULL OR value_date IS NULL OR maturity_date >= value_date),

    -- 索引定义
    UNIQUE KEY uk_execution_id (execution_id),
    KEY idx_order_no (order_no),
    KEY idx_symbol_time (symbol, execution_time),
    KEY idx_channel_time (channel_code, execution_time),
    KEY idx_execution_time (execution_time),
    KEY idx_side_symbol (side, symbol),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单成交记录表';
```

### 7.3 PostgreSQL版本

```sql
-- 创建枚举类型
CREATE TYPE execution_side_enum AS ENUM ('BUY', 'SELL');
CREATE TYPE effect_type_enum AS ENUM ('OPEN', 'CLOSE', 'CLOSE_TODAY', 'CLOSE_YESTERDAY');
CREATE TYPE market_type_enum AS ENUM ('INTERNAL', 'EXTERNAL', 'BOTH');

-- 创建表
CREATE TABLE order_executions (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(64) NOT NULL,
    execution_id VARCHAR(64) NOT NULL UNIQUE,
    execution_time TIMESTAMP NOT NULL,
    channel_code VARCHAR(32) NOT NULL,
    symbol VARCHAR(32) NOT NULL,
    side execution_side_enum NOT NULL,
    execution_price DECIMAL(18,8) NOT NULL CHECK (execution_price > 0),
    execution_quantity DECIMAL(18,8) NOT NULL CHECK (execution_quantity > 0),
    effect_type effect_type_enum NOT NULL,
    order_type INTEGER NOT NULL,
    value_date VARCHAR(8),
    maturity_date VARCHAR(8),
    commission DECIMAL(18,8) DEFAULT 0.00 CHECK (commission >= 0),
    execution_amount DECIMAL(18,2),
    currency VARCHAR(8),
    counterparty_id VARCHAR(64),
    counterparty_name VARCHAR(128),
    trader_id VARCHAR(64),
    trader_name VARCHAR(128),
    external_execution_id VARCHAR(64),
    settlement_date DATE,
    market_type market_type_enum NOT NULL DEFAULT 'EXTERNAL',
    execution_venue VARCHAR(64),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    version INTEGER NOT NULL DEFAULT 1 CHECK (version > 0),
    remark TEXT,

    -- 约束检查
    CONSTRAINT check_value_date_format CHECK (value_date IS NULL OR value_date ~ '^[0-9]{8}$'),
    CONSTRAINT check_maturity_date_format CHECK (maturity_date IS NULL OR maturity_date ~ '^[0-9]{8}$'),
    CONSTRAINT check_maturity_after_value CHECK (maturity_date IS NULL OR value_date IS NULL OR maturity_date >= value_date)
);

-- 创建索引
CREATE UNIQUE INDEX uk_execution_id ON order_executions(execution_id);
CREATE INDEX idx_order_no ON order_executions(order_no);
CREATE INDEX idx_symbol_time ON order_executions(symbol, execution_time);
CREATE INDEX idx_channel_time ON order_executions(channel_code, execution_time);
CREATE INDEX idx_execution_time ON order_executions(execution_time);
CREATE INDEX idx_side_symbol ON order_executions(side, symbol);
CREATE INDEX idx_create_time ON order_executions(create_time);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_order_executions_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_order_executions_timestamp
    BEFORE UPDATE ON order_executions
    FOR EACH ROW
    EXECUTE FUNCTION update_order_executions_timestamp();

-- 添加表注释
COMMENT ON TABLE order_executions IS '订单成交记录表';
COMMENT ON COLUMN order_executions.id IS '主键ID';
COMMENT ON COLUMN order_executions.order_no IS '订单号';
COMMENT ON COLUMN order_executions.execution_id IS '成交号';
COMMENT ON COLUMN order_executions.execution_time IS '成交时间';
COMMENT ON COLUMN order_executions.channel_code IS '渠道编号';
COMMENT ON COLUMN order_executions.symbol IS '合约代码';
COMMENT ON COLUMN order_executions.side IS '成交方向';
COMMENT ON COLUMN order_executions.execution_price IS '成交价格';
COMMENT ON COLUMN order_executions.execution_quantity IS '成交量';
COMMENT ON COLUMN order_executions.effect_type IS '开平仓类型';
COMMENT ON COLUMN order_executions.order_type IS '订单类型';
COMMENT ON COLUMN order_executions.value_date IS '近端交割日';
COMMENT ON COLUMN order_executions.maturity_date IS '远端交割日';
```

## 8. SQLAlchemy模型定义

### 8.1 完整模型代码

```python
"""
OrderExecution SQLAlchemy 模型定义

该模型用于存储订单成交记录，记录所有订单的实际成交信息
"""

from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Enum, Boolean,
    Text, Index, CheckConstraint, UniqueConstraint, DECIMAL, Date, BigInteger
)
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declarative_base
import enum
from datetime import datetime
from typing import Optional, Dict, Any

Base = declarative_base()


# 成交方向枚举
class ExecutionSide(str, enum.Enum):
    BUY = "BUY"      # 买入
    SELL = "SELL"    # 卖出


# 开平仓类型枚举
class EffectType(str, enum.Enum):
    OPEN = "OPEN"                    # 开仓
    CLOSE = "CLOSE"                  # 平仓
    CLOSE_TODAY = "CLOSE_TODAY"      # 平今
    CLOSE_YESTERDAY = "CLOSE_YESTERDAY"  # 平昨


# 订单类型枚举
class OrderType(int, enum.Enum):
    MARKET = 0          # 市价单
    LIMIT = 2           # 限价单
    SOR = 15            # SOR单
    DIRECT_LIMIT = 28   # 直通限价单
    BEST_QUOTE = 50     # 择优询价单
    STOP_LIMIT = 52     # 止损限价单
    STOP = 54           # 止损单


# 市场类型枚举
class MarketType(str, enum.Enum):
    INTERNAL = "INTERNAL"     # 内部市场
    EXTERNAL = "EXTERNAL"     # 外部市场
    BOTH = "BOTH"            # 内外部市场


class OrderExecution(Base):
    """
    订单成交记录模型

    该模型存储所有订单的成交信息，用于记录实际的交易执行结果
    """
    __tablename__ = "order_executions"

    # 主键和唯一标识
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")
    order_no = Column(String(64), nullable=False, index=True, comment="订单号")
    execution_id = Column(String(64), unique=True, nullable=False, comment="成交号")

    # 成交基本信息
    execution_time = Column(DateTime, nullable=False, comment="成交时间")
    channel_code = Column(String(32), nullable=False, index=True, comment="渠道编号")
    symbol = Column(String(32), nullable=False, index=True, comment="合约代码")
    side = Column(Enum(ExecutionSide), nullable=False, comment="成交方向")

    # 价格和数量信息
    execution_price = Column(DECIMAL(18, 8), nullable=False, comment="成交价格")
    execution_quantity = Column(DECIMAL(18, 8), nullable=False, comment="成交量")
    execution_amount = Column(DECIMAL(18, 2), nullable=True, comment="成交金额")
    commission = Column(DECIMAL(18, 8), default=0.00, nullable=True, comment="手续费")

    # 订单属性
    effect_type = Column(Enum(EffectType), nullable=False, comment="开平仓类型")
    order_type = Column(Integer, nullable=False, comment="订单类型")
    market_type = Column(Enum(MarketType), default=MarketType.EXTERNAL, nullable=False, comment="执行市场类型")

    # 交割日期信息
    value_date = Column(String(8), nullable=True, comment="近端交割日 (yyyyMMdd)")
    maturity_date = Column(String(8), nullable=True, comment="远端交割日 (yyyyMMdd)")
    settlement_date = Column(Date, nullable=True, comment="结算日期")

    # 货币和场所信息
    currency = Column(String(8), nullable=True, comment="计价货币")
    execution_venue = Column(String(64), nullable=True, comment="成交场所")

    # 对手方信息
    counterparty_id = Column(String(64), nullable=True, comment="对手方ID")
    counterparty_name = Column(String(128), nullable=True, comment="对手方名称")
    trader_id = Column(String(64), nullable=True, comment="交易员ID")
    trader_name = Column(String(128), nullable=True, comment="交易员姓名")

    # 外部系统关联
    external_execution_id = Column(String(64), nullable=True, comment="外部系统成交ID")

    # 时间戳
    create_time = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment="更新时间")

    # 审计字段
    created_by = Column(String(64), nullable=True, comment="创建者")
    updated_by = Column(String(64), nullable=True, comment="更新者")
    version = Column(Integer, default=1, nullable=False, comment="版本号")
    remark = Column(Text, nullable=True, comment="备注信息")

    # 表级约束
    __table_args__ = (
        # 索引定义
        Index('idx_symbol_time', 'symbol', 'execution_time'),
        Index('idx_channel_time', 'channel_code', 'execution_time'),
        Index('idx_execution_time', 'execution_time'),
        Index('idx_side_symbol', 'side', 'symbol'),
        Index('idx_create_time', 'create_time'),

        # 检查约束
        CheckConstraint('execution_price > 0', name='check_positive_execution_price'),
        CheckConstraint('execution_quantity > 0', name='check_positive_execution_quantity'),
        CheckConstraint('commission >= 0', name='check_non_negative_commission'),
        CheckConstraint('version > 0', name='check_positive_version'),

        # 表注释
        {'comment': '订单成交记录表 - 存储所有订单的实际成交信息'}
    )

    def __repr__(self) -> str:
        return (f"<OrderExecution(id={self.id}, execution_id='{self.execution_id}', "
                f"symbol='{self.symbol}', side='{self.side}', "
                f"price={self.execution_price}, quantity={self.execution_quantity})>")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_no': self.order_no,
            'execution_id': self.execution_id,
            'execution_time': self.execution_time.isoformat() if self.execution_time else None,
            'channel_code': self.channel_code,
            'symbol': self.symbol,
            'side': self.side.value if self.side else None,
            'execution_price': float(self.execution_price) if self.execution_price else None,
            'execution_quantity': float(self.execution_quantity) if self.execution_quantity else None,
            'execution_amount': float(self.execution_amount) if self.execution_amount else None,
            'commission': float(self.commission) if self.commission else None,
            'effect_type': self.effect_type.value if self.effect_type else None,
            'order_type': self.order_type,
            'market_type': self.market_type.value if self.market_type else None,
            'value_date': self.value_date,
            'maturity_date': self.maturity_date,
            'settlement_date': self.settlement_date.isoformat() if self.settlement_date else None,
            'currency': self.currency,
            'execution_venue': self.execution_venue,
            'counterparty_id': self.counterparty_id,
            'counterparty_name': self.counterparty_name,
            'trader_id': self.trader_id,
            'trader_name': self.trader_name,
            'external_execution_id': self.external_execution_id,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'version': self.version,
            'remark': self.remark
        }

    @classmethod
    def from_trade_data(cls, trade_data: Dict[str, Any]) -> 'OrderExecution':
        """
        从交易数据创建成交记录

        Args:
            trade_data: 交易数据字典

        Returns:
            OrderExecution实例
        """
        return cls(
            order_no=trade_data.get('order_id'),
            execution_id=trade_data.get('trade_id'),
            execution_time=trade_data.get('trade_time'),
            channel_code=trade_data.get('channel_code'),
            symbol=trade_data.get('symbol'),
            side=ExecutionSide(trade_data.get('side')),
            execution_price=trade_data.get('price'),
            execution_quantity=trade_data.get('quantity'),
            execution_amount=trade_data.get('amount'),
            commission=trade_data.get('commission', 0.00),
            effect_type=EffectType(trade_data.get('effect_type')),
            order_type=trade_data.get('order_type'),
            market_type=MarketType(trade_data.get('market_type', 'EXTERNAL')),
            value_date=trade_data.get('value_date'),
            maturity_date=trade_data.get('maturity_date'),
            currency=trade_data.get('currency'),
            counterparty_id=trade_data.get('counterparty_id'),
            counterparty_name=trade_data.get('counterparty_name'),
            trader_id=trade_data.get('trader_id'),
            trader_name=trade_data.get('trader_name'),
            external_execution_id=trade_data.get('external_execution_id'),
            created_by=trade_data.get('created_by'),
            remark=trade_data.get('remark')
        )
```

## 9. 使用示例

### 9.1 创建成交记录

```python
from custom_api.database import get_db_session
from models.order_execution import OrderExecution, ExecutionSide, EffectType, MarketType
from datetime import datetime

# 创建成交记录
with get_db_session() as session:
    execution = OrderExecution(
        order_no="ORDER20241212001",
        execution_id="EXEC20241212001",
        execution_time=datetime.now(),
        channel_code="CHANNEL001",
        symbol="EURUSD",
        side=ExecutionSide.BUY,
        execution_price=1.10500,
        execution_quantity=100000,
        execution_amount=110500.00,
        commission=5.50,
        effect_type=EffectType.OPEN,
        order_type=2,  # 限价单
        market_type=MarketType.EXTERNAL,
        currency="USD",
        created_by="trader001"
    )

    session.add(execution)
    session.commit()
    print(f"成交记录已创建，ID: {execution.id}")
```

### 9.2 查询成交记录

```python
# 按成交号查询
with get_db_session() as session:
    execution = session.query(OrderExecution).filter_by(
        execution_id="EXEC20241212001"
    ).first()

    if execution:
        print(f"成交信息: {execution.to_dict()}")

# 按订单号查询所有成交
with get_db_session() as session:
    executions = session.query(OrderExecution).filter_by(
        order_no="ORDER20241212001"
    ).order_by(OrderExecution.execution_time).all()

    total_quantity = sum(e.execution_quantity for e in executions)
    avg_price = sum(e.execution_price * e.execution_quantity for e in executions) / total_quantity

    print(f"订单总成交量: {total_quantity}")
    print(f"平均成交价: {avg_price:.5f}")
```

### 9.3 统计查询

```python
from sqlalchemy import func
from datetime import datetime, timedelta

# 按合约统计今日成交
today = datetime.now().date()
with get_db_session() as session:
    stats = session.query(
        OrderExecution.symbol,
        func.count(OrderExecution.id).label('execution_count'),
        func.sum(OrderExecution.execution_quantity).label('total_quantity'),
        func.sum(OrderExecution.execution_amount).label('total_amount'),
        func.avg(OrderExecution.execution_price).label('avg_price')
    ).filter(
        func.date(OrderExecution.execution_time) == today
    ).group_by(OrderExecution.symbol).all()

    for stat in stats:
        print(f"合约: {stat.symbol}, 成交笔数: {stat.execution_count}, "
              f"总量: {stat.total_quantity}, 总金额: {stat.total_amount}")
```

## 10. 最佳实践

### 10.1 数据插入优化

```python
# 批量插入成交记录
def batch_insert_executions(execution_data_list):
    """批量插入成交记录"""
    with get_db_session() as session:
        executions = []
        for data in execution_data_list:
            execution = OrderExecution.from_trade_data(data)
            executions.append(execution)

        # 批量插入
        session.bulk_save_objects(executions)
        session.commit()

        return len(executions)

# 使用示例
trade_data_list = [
    {
        'order_id': 'ORDER001',
        'trade_id': 'TRADE001',
        'trade_time': datetime.now(),
        'channel_code': 'CH001',
        'symbol': 'EURUSD',
        'side': 'BUY',
        'price': 1.1050,
        'quantity': 50000,
        'effect_type': 'OPEN',
        'order_type': 2
    },
    # ... 更多数据
]

count = batch_insert_executions(trade_data_list)
print(f"批量插入 {count} 条成交记录")
```

### 10.2 查询性能优化

```python
# 使用索引优化查询
def get_symbol_executions_by_date_range(symbol, start_date, end_date, limit=1000):
    """按日期范围查询合约成交记录（使用复合索引）"""
    with get_db_session() as session:
        return session.query(OrderExecution).filter(
            OrderExecution.symbol == symbol,
            OrderExecution.execution_time >= start_date,
            OrderExecution.execution_time <= end_date
        ).order_by(OrderExecution.execution_time.desc()).limit(limit).all()

# 分页查询
def get_executions_paginated(page=1, page_size=50, symbol=None):
    """分页查询成交记录"""
    with get_db_session() as session:
        query = session.query(OrderExecution)

        if symbol:
            query = query.filter(OrderExecution.symbol == symbol)

        total = query.count()
        executions = query.order_by(
            OrderExecution.execution_time.desc()
        ).offset((page - 1) * page_size).limit(page_size).all()

        return {
            'executions': [e.to_dict() for e in executions],
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total,
                'pages': (total + page_size - 1) // page_size
            }
        }
```

### 10.3 数据完整性保证

```python
# 成交记录验证
def validate_execution_data(execution_data):
    """验证成交数据完整性"""
    required_fields = [
        'order_no', 'execution_id', 'execution_time',
        'channel_code', 'symbol', 'side',
        'execution_price', 'execution_quantity', 'effect_type'
    ]

    for field in required_fields:
        if not execution_data.get(field):
            raise ValueError(f"缺少必填字段: {field}")

    # 价格和数量验证
    if execution_data['execution_price'] <= 0:
        raise ValueError("成交价格必须大于0")

    if execution_data['execution_quantity'] <= 0:
        raise ValueError("成交数量必须大于0")

    # 日期格式验证
    for date_field in ['value_date', 'maturity_date']:
        if execution_data.get(date_field):
            date_str = execution_data[date_field]
            if not (len(date_str) == 8 and date_str.isdigit()):
                raise ValueError(f"{date_field} 格式错误，应为yyyyMMdd")

    return True

# 使用示例
try:
    validate_execution_data(trade_data)
    execution = OrderExecution.from_trade_data(trade_data)
    # 保存到数据库
except ValueError as e:
    print(f"数据验证失败: {e}")
```

## 11. 性能优化建议

### 11.1 索引策略

1. **复合索引优先级**:
   - `(symbol, execution_time)`: 按合约查询历史成交
   - `(channel_code, execution_time)`: 按渠道统计分析
   - `(side, symbol)`: 按方向和合约分析

2. **分区表策略** (适用于大数据量):
   ```sql
   -- MySQL 按月分区示例
   ALTER TABLE order_executions
   PARTITION BY RANGE (YEAR(execution_time) * 100 + MONTH(execution_time)) (
       PARTITION p202401 VALUES LESS THAN (202402),
       PARTITION p202402 VALUES LESS THAN (202403),
       -- ... 更多分区
   );
   ```

### 11.2 查询优化

1. **避免全表扫描**:
   - 查询时始终包含索引字段
   - 使用 LIMIT 限制结果集大小
   - 避免在 WHERE 子句中使用函数

2. **批量操作**:
   - 使用 `bulk_insert_mappings()` 进行批量插入
   - 使用 `bulk_update_mappings()` 进行批量更新

### 11.3 存储优化

1. **数据类型选择**:
   - 使用 DECIMAL 而非 FLOAT 存储金额
   - 合理设置字符串字段长度
   - 使用 ENUM 替代字符串常量

2. **历史数据归档**:
   ```python
   # 定期归档历史数据
   def archive_old_executions(days_to_keep=365):
       """归档超过指定天数的成交记录"""
       cutoff_date = datetime.now() - timedelta(days=days_to_keep)

       with get_db_session() as session:
           # 将旧数据移动到归档表
           old_executions = session.query(OrderExecution).filter(
               OrderExecution.execution_time < cutoff_date
           ).all()

           # 这里可以将数据导出到文件或归档表
           # 然后删除原表中的旧数据
           session.query(OrderExecution).filter(
               OrderExecution.execution_time < cutoff_date
           ).delete()

           session.commit()
           return len(old_executions)
   ```

## 12. 监控和维护

### 12.1 数据质量监控

```python
def check_data_quality():
    """检查数据质量"""
    with get_db_session() as session:
        # 检查重复成交号
        duplicate_executions = session.query(
            OrderExecution.execution_id,
            func.count(OrderExecution.id).label('count')
        ).group_by(OrderExecution.execution_id).having(
            func.count(OrderExecution.id) > 1
        ).all()

        if duplicate_executions:
            print(f"发现 {len(duplicate_executions)} 个重复成交号")

        # 检查异常价格
        abnormal_prices = session.query(OrderExecution).filter(
            OrderExecution.execution_price <= 0
        ).count()

        if abnormal_prices > 0:
            print(f"发现 {abnormal_prices} 条异常价格记录")

        # 检查数据完整性
        incomplete_records = session.query(OrderExecution).filter(
            OrderExecution.order_no.is_(None) |
            OrderExecution.execution_id.is_(None) |
            OrderExecution.symbol.is_(None)
        ).count()

        if incomplete_records > 0:
            print(f"发现 {incomplete_records} 条不完整记录")
```

### 12.2 性能监控

```python
import time
from functools import wraps

def monitor_query_performance(func):
    """查询性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        execution_time = end_time - start_time
        if execution_time > 1.0:  # 超过1秒的查询
            print(f"慢查询警告: {func.__name__} 执行时间 {execution_time:.2f}秒")

        return result
    return wrapper

@monitor_query_performance
def get_large_dataset(symbol, days=30):
    """获取大数据集示例"""
    start_date = datetime.now() - timedelta(days=days)
    return get_symbol_executions_by_date_range(symbol, start_date, datetime.now())
```

## 13. 总结

本设计文档提供了完整的量化交易系统订单成交表设计方案，包括：

1. **完整的表结构定义**：涵盖所有必要字段和业务场景
2. **多数据库支持**：提供SQLite、MySQL、PostgreSQL的建表语句
3. **SQLAlchemy模型**：完整的ORM模型定义和使用方法
4. **性能优化**：索引设计、查询优化、批量操作等最佳实践
5. **数据完整性**：约束检查、验证规则、监控方案

该设计方案充分考虑了量化交易业务的特点，支持多种交易类型和市场，具有良好的扩展性和维护性。建议在实际使用中根据具体业务需求进行适当调整。
```
