"""
持仓系统使用示例

演示如何使用持仓表模型进行基本的CRUD操作和业务逻辑
"""

import os
import sys
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from custom_api.models import Position, PositionSide, PositionStatus, PositionType
from custom_api.dao import get_position_dao
from custom_api.database.connection import init_database, get_db_session, cleanup_database


def main():
    """主函数"""
    
    print("=== 持仓系统使用示例 ===\n")
    
    try:
        # 1. 初始化数据库
        print("1. 初始化数据库...")
        init_database()
        print("   ✓ 数据库初始化成功\n")
        
        # 2. 获取DAO实例
        position_dao = get_position_dao()
        
        # 3. 创建示例持仓
        print("2. 创建示例持仓...")
        
        # 生成唯一标识符
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        
        # 黄金多头持仓
        gold_long_position = {
            'position_id': f'POS{timestamp}001_{unique_id}',
            'open_order_id': f'ORD{timestamp}001',
            'channel_code': 'CHANNEL001',
            'symbol': 'XAUUSD',
            'position_side': PositionSide.LONG,
            'position_status': PositionStatus.OPEN,
            'position_type': PositionType.NORMAL,
            'open_time': datetime.now() - timedelta(hours=2),
            'open_price': Decimal('2650.50'),
            'current_price': Decimal('2655.80'),
            'total_quantity': Decimal('1.0'),
            'available_quantity': Decimal('1.0'),
            'frozen_quantity': Decimal('0.0'),
            'today_quantity': Decimal('1.0'),
            'position_cost': Decimal('2650.50'),
            'commission': Decimal('5.30'),
            'swap_fee': Decimal('0.50'),
            'stop_loss_price': Decimal('2620.00'),
            'take_profit_price': Decimal('2680.00'),
            'is_active': True,
            'remark': '黄金多头持仓'
        }
        
        # 欧美空头持仓
        eur_short_position = {
            'position_id': f'POS{timestamp}002_{unique_id}',
            'open_order_id': f'ORD{timestamp}002',
            'channel_code': 'CHANNEL001',
            'symbol': 'EURUSD',
            'position_side': PositionSide.SHORT,
            'position_status': PositionStatus.OPEN,
            'position_type': PositionType.NORMAL,
            'open_time': datetime.now() - timedelta(hours=1),
            'open_price': Decimal('1.0550'),
            'current_price': Decimal('1.0540'),
            'total_quantity': Decimal('10000.0'),
            'available_quantity': Decimal('10000.0'),
            'frozen_quantity': Decimal('0.0'),
            'today_quantity': Decimal('10000.0'),
            'position_cost': Decimal('10550.00'),
            'commission': Decimal('2.10'),
            'swap_fee': Decimal('1.20'),
            'is_active': True,
            'remark': '欧美空头持仓'
        }
        
        # 创建持仓
        gold_position = position_dao.create(gold_long_position)
        eur_position = position_dao.create(eur_short_position)
        
        # 更新持仓盈亏
        gold_position.update_current_price(gold_position.current_price)
        eur_position.update_current_price(eur_position.current_price)
        
        print(f"   ✓ 创建黄金多头持仓: {gold_position.position_id}")
        print(f"   ✓ 创建欧美空头持仓: {eur_position.position_id}\n")
        
        # 4. 查询持仓
        print("3. 查询持仓...")
        
        # 根据合约查询
        xauusd_positions = position_dao.get_by_symbol('XAUUSD')
        print(f"   XAUUSD 持仓数: {len(xauusd_positions)}")
        
        eurusd_positions = position_dao.get_by_symbol('EURUSD')
        print(f"   EURUSD 持仓数: {len(eurusd_positions)}")
        
        # 根据持仓方向查询
        long_positions = position_dao.get_by_side(PositionSide.LONG)
        print(f"   多头持仓数: {len(long_positions)}")
        
        short_positions = position_dao.get_by_side(PositionSide.SHORT)
        print(f"   空头持仓数: {len(short_positions)}")
        
        # 获取活跃持仓
        active_positions = position_dao.get_active_positions()
        print(f"   活跃持仓数: {len(active_positions)}\n")
        
        # 5. 价格更新和盈亏计算
        print("4. 价格更新和盈亏计算...")
        
        # 模拟价格变动
        new_prices = [
            {'symbol': 'XAUUSD', 'price': 2665.00},  # 黄金上涨
            {'symbol': 'EURUSD', 'price': 1.0530}    # 欧美下跌
        ]
        
        updated_count = position_dao.update_current_prices(new_prices)
        print(f"   更新了 {updated_count} 个持仓的价格")
        
        # 重新查询持仓查看盈亏
        updated_gold = position_dao.get_by_position_id(gold_position.position_id)
        updated_eur = position_dao.get_by_position_id(eur_position.position_id)
        
        print(f"   黄金持仓盈亏:")
        print(f"     开仓价格: {updated_gold.open_price}")
        print(f"     当前价格: {updated_gold.current_price}")
        print(f"     未实现盈亏: {updated_gold.unrealized_pnl}")
        print(f"     总盈亏: {updated_gold.total_pnl}")
        print(f"     收益率: {updated_gold.get_return_rate():.2f}%")
        
        print(f"   欧美持仓盈亏:")
        print(f"     开仓价格: {updated_eur.open_price}")
        print(f"     当前价格: {updated_eur.current_price}")
        print(f"     未实现盈亏: {updated_eur.unrealized_pnl}")
        print(f"     总盈亏: {updated_eur.total_pnl}")
        print(f"     收益率: {updated_eur.get_return_rate():.2f}%\n")
        
        # 6. 统计分析
        print("5. 统计分析...")
        
        # 整体统计
        overall_stats = position_dao.get_overall_statistics()
        print(f"   整体持仓统计:")
        print(f"     总持仓数: {overall_stats['total_count']}")
        print(f"     活跃持仓数: {overall_stats['active_count']}")
        print(f"     多头持仓数: {overall_stats['long_count']}")
        print(f"     空头持仓数: {overall_stats['short_count']}")
        print(f"     总成本: {overall_stats['total_cost']:.2f}")
        print(f"     总市值: {overall_stats['total_market_value']:.2f}")
        print(f"     总盈亏: {overall_stats['total_pnl']:.2f}")
        print(f"     胜率: {overall_stats['win_rate']:.2f}%")
        print(f"     总收益率: {overall_stats['total_return_rate']:.2f}%")
        
        # 按合约统计
        xauusd_stats = position_dao.get_statistics_by_symbol('XAUUSD')
        print(f"   XAUUSD 持仓统计:")
        print(f"     持仓数: {xauusd_stats['total_count']}")
        print(f"     总持仓量: {xauusd_stats['total_quantity']}")
        print(f"     平均开仓价: {xauusd_stats['avg_open_price']:.2f}")
        print(f"     总盈亏: {xauusd_stats['total_pnl']:.2f}\n")
        
        # 7. 复合搜索
        print("6. 复合搜索...")
        search_filters = {
            'position_status': PositionStatus.OPEN,
            'is_active': True,
            'min_pnl': 0  # 只查询盈利的持仓
        }
        
        profitable_positions = position_dao.search_positions(search_filters)
        print(f"   盈利的活跃持仓数量: {len(profitable_positions)}")
        
        for pos in profitable_positions:
            print(f"     {pos.symbol} {pos.position_side.value}: 盈亏 {pos.total_pnl:.2f}")
        
        # 8. 模拟平仓
        print("\n7. 模拟平仓...")
        
        # 平仓黄金持仓
        close_price = Decimal('2670.00')
        closed_position = position_dao.close_position(
            position_id=gold_position.position_id,
            close_price=close_price,
            close_order_id=f'CLOSE{timestamp}001'
        )
        
        if closed_position:
            print(f"   ✓ 平仓成功: {closed_position.symbol}")
            print(f"     开仓价格: {closed_position.open_price}")
            print(f"     平仓价格: {closed_position.close_price}")
            print(f"     已实现盈亏: {closed_position.realized_pnl}")
            print(f"     总盈亏: {closed_position.total_pnl}")
        
        print("\n=== 示例执行完成 ===")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理数据库资源
        cleanup_database()


if __name__ == "__main__":
    main()
