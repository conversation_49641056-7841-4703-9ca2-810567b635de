"""
持仓与订单集成示例

演示持仓表与订单表之间的业务逻辑关系和数据流转
"""

import os
import sys
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from custom_api.models import (
    Order, SideType as OrderSideType, EffectType, OrderType,
    Position, PositionSide, PositionStatus, PositionType
)
from custom_api.dao import get_order_dao, get_position_dao
from custom_api.database.connection import init_database, get_db_session, cleanup_database


class TradingSystem:
    """交易系统业务逻辑类"""
    
    def __init__(self):
        self.order_dao = get_order_dao()
        self.position_dao = get_position_dao()
    
    def process_open_order(self, order_data: dict) -> tuple:
        """
        处理开仓订单，创建订单记录和持仓记录
        
        Args:
            order_data: 订单数据
            
        Returns:
            (order, position) 元组
        """
        # 1. 创建订单记录
        order = self.order_dao.create(order_data)
        
        # 2. 根据订单创建持仓记录
        position_data = {
            'position_id': f"POS_{order.order_no}_{order.execution_id}",
            'open_order_id': order.order_no,
            'channel_code': order.channel_code,
            'symbol': order.symbol,
            'position_side': PositionSide.LONG if order.side == OrderSideType.BUY else PositionSide.SHORT,
            'position_status': PositionStatus.OPEN,
            'position_type': PositionType.NORMAL,
            'open_time': order.execution_time,
            'open_price': order.execution_price,
            'current_price': order.execution_price,
            'total_quantity': order.execution_quantity,
            'available_quantity': order.execution_quantity,
            'frozen_quantity': Decimal('0.0'),
            'today_quantity': order.execution_quantity,
            'position_cost': order.execution_price * order.execution_quantity,
            'commission': order.commission,
            'swap_fee': Decimal('0.0'),
            'is_active': True,
            'remark': f'由订单 {order.order_no} 开仓'
        }
        
        position = self.position_dao.create(position_data)
        
        print(f"✓ 开仓成功: {order.symbol} {order.side.value} {order.execution_quantity} @ {order.execution_price}")
        print(f"  订单ID: {order.order_no}")
        print(f"  持仓ID: {position.position_id}")
        
        return order, position
    
    def process_close_order(self, close_order_data: dict, position_id: str) -> tuple:
        """
        处理平仓订单，更新订单记录和持仓状态
        
        Args:
            close_order_data: 平仓订单数据
            position_id: 要平仓的持仓ID
            
        Returns:
            (order, position) 元组
        """
        # 1. 创建平仓订单记录
        close_order = self.order_dao.create(close_order_data)
        
        # 2. 更新持仓状态
        position = self.position_dao.close_position(
            position_id=position_id,
            close_price=close_order.execution_price,
            close_order_id=close_order.order_no
        )
        
        if position:
            print(f"✓ 平仓成功: {close_order.symbol} {close_order.side.value} {close_order.execution_quantity} @ {close_order.execution_price}")
            print(f"  平仓订单ID: {close_order.order_no}")
            print(f"  持仓ID: {position.position_id}")
            print(f"  已实现盈亏: {position.realized_pnl}")
        
        return close_order, position
    
    def get_position_orders(self, position_id: str) -> dict:
        """
        获取持仓相关的所有订单
        
        Args:
            position_id: 持仓ID
            
        Returns:
            包含开仓和平仓订单的字典
        """
        position = self.position_dao.get_by_position_id(position_id)
        if not position:
            return {}
        
        # 获取开仓订单
        open_orders = self.order_dao.get_by_order_no(position.open_order_id)
        
        # 获取平仓订单
        close_orders = []
        if position.close_order_id:
            close_orders = self.order_dao.get_by_order_no(position.close_order_id)
        
        return {
            'position': position,
            'open_orders': open_orders,
            'close_orders': close_orders
        }
    
    def calculate_position_pnl_from_orders(self, position_id: str) -> dict:
        """
        基于订单数据计算持仓盈亏
        
        Args:
            position_id: 持仓ID
            
        Returns:
            盈亏计算结果
        """
        orders_info = self.get_position_orders(position_id)
        position = orders_info.get('position')
        
        if not position:
            return {}
        
        open_orders = orders_info.get('open_orders', [])
        close_orders = orders_info.get('close_orders', [])
        
        # 计算开仓成本
        total_open_cost = sum(order.execution_price * order.execution_quantity for order in open_orders)
        total_open_quantity = sum(order.execution_quantity for order in open_orders)
        avg_open_price = total_open_cost / total_open_quantity if total_open_quantity > 0 else 0
        
        # 计算平仓收入
        total_close_value = sum(order.execution_price * order.execution_quantity for order in close_orders)
        total_close_quantity = sum(order.execution_quantity for order in close_orders)
        avg_close_price = total_close_value / total_close_quantity if total_close_quantity > 0 else 0
        
        # 计算手续费
        total_commission = sum(order.commission for order in open_orders + close_orders)
        
        # 计算盈亏
        if position.position_status == PositionStatus.CLOSED:
            # 已平仓：计算已实现盈亏
            if position.position_side == PositionSide.LONG:
                pnl = (avg_close_price - avg_open_price) * total_close_quantity
            else:
                pnl = (avg_open_price - avg_close_price) * total_close_quantity
            
            net_pnl = pnl - total_commission
        else:
            # 未平仓：使用当前价格计算未实现盈亏
            current_price = position.current_price or avg_open_price
            if position.position_side == PositionSide.LONG:
                pnl = (current_price - avg_open_price) * position.total_quantity
            else:
                pnl = (avg_open_price - current_price) * position.total_quantity
            
            net_pnl = pnl - total_commission
        
        return {
            'position_id': position_id,
            'avg_open_price': avg_open_price,
            'avg_close_price': avg_close_price,
            'total_commission': total_commission,
            'gross_pnl': pnl,
            'net_pnl': net_pnl,
            'return_rate': (net_pnl / total_open_cost * 100) if total_open_cost > 0 else 0
        }


def main():
    """主函数"""
    
    print("=== 持仓与订单集成示例 ===\n")
    
    try:
        # 1. 初始化数据库
        print("1. 初始化数据库...")
        init_database()
        print("   ✓ 数据库初始化成功\n")
        
        # 2. 创建交易系统实例
        trading_system = TradingSystem()
        
        # 3. 模拟开仓交易
        print("2. 模拟开仓交易...")
        
        # 生成唯一标识符
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        
        # 开仓订单数据
        open_order_data = {
            'order_no': f'ORD{timestamp}001',
            'execution_id': f'EXE{timestamp}001001_{unique_id}',
            'execution_time': datetime.now(),
            'channel_code': 'CHANNEL001',
            'symbol': 'XAUUSD',
            'side': OrderSideType.BUY,
            'execution_price': Decimal('2650.50'),
            'execution_quantity': Decimal('1.0'),
            'effect_type': EffectType.OPEN,
            'order_type': OrderType.MARKET,
            'commission': Decimal('5.30'),
            'remark': '黄金多头开仓'
        }
        
        # 处理开仓
        open_order, position = trading_system.process_open_order(open_order_data)
        print()
        
        # 4. 模拟价格变动
        print("3. 模拟价格变动...")
        new_price = Decimal('2665.00')
        position.update_current_price(new_price)
        
        # 更新数据库中的持仓
        with get_db_session() as session:
            db_position = session.query(Position).filter_by(id=position.id).first()
            if db_position:
                db_position.current_price = new_price
                db_position.unrealized_pnl = position.unrealized_pnl
                db_position.total_pnl = position.total_pnl
                db_position.market_value = position.market_value
        
        print(f"   价格更新: {open_order.execution_price} -> {new_price}")
        print(f"   未实现盈亏: {position.unrealized_pnl}")
        print(f"   收益率: {position.get_return_rate():.2f}%\n")
        
        # 5. 查询持仓相关订单
        print("4. 查询持仓相关订单...")
        orders_info = trading_system.get_position_orders(position.position_id)
        print(f"   持仓ID: {orders_info['position'].position_id}")
        print(f"   开仓订单数: {len(orders_info['open_orders'])}")
        print(f"   平仓订单数: {len(orders_info['close_orders'])}")
        
        # 基于订单计算盈亏
        pnl_info = trading_system.calculate_position_pnl_from_orders(position.position_id)
        print(f"   基于订单计算的盈亏: {pnl_info['net_pnl']:.2f}")
        print(f"   收益率: {pnl_info['return_rate']:.2f}%\n")
        
        # 6. 模拟平仓交易
        print("5. 模拟平仓交易...")
        
        # 平仓订单数据
        close_order_data = {
            'order_no': f'ORD{timestamp}002',
            'execution_id': f'EXE{timestamp}002001_{unique_id}',
            'execution_time': datetime.now() + timedelta(minutes=30),
            'channel_code': 'CHANNEL001',
            'symbol': 'XAUUSD',
            'side': OrderSideType.SELL,
            'execution_price': Decimal('2670.00'),
            'execution_quantity': Decimal('1.0'),
            'effect_type': EffectType.CLOSE,
            'order_type': OrderType.MARKET,
            'commission': Decimal('5.30'),
            'remark': '黄金多头平仓'
        }
        
        # 处理平仓
        close_order, closed_position = trading_system.process_close_order(
            close_order_data, position.position_id
        )
        print()
        
        # 7. 最终结果分析
        print("6. 最终结果分析...")
        final_orders_info = trading_system.get_position_orders(position.position_id)
        final_pnl_info = trading_system.calculate_position_pnl_from_orders(position.position_id)
        
        print(f"   交易完成:")
        print(f"     开仓价格: {final_pnl_info['avg_open_price']}")
        print(f"     平仓价格: {final_pnl_info['avg_close_price']}")
        print(f"     总手续费: {final_pnl_info['total_commission']}")
        print(f"     毛盈亏: {final_pnl_info['gross_pnl']:.2f}")
        print(f"     净盈亏: {final_pnl_info['net_pnl']:.2f}")
        print(f"     收益率: {final_pnl_info['return_rate']:.2f}%")
        
        print("\n=== 集成示例执行完成 ===")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理数据库资源
        cleanup_database()


if __name__ == "__main__":
    main()
