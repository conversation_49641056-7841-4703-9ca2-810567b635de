"""
订单模型使用示例

演示如何使用订单表模型进行基本的CRUD操作
"""

import os
import sys
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from custom_api.models import Order, SideType, EffectType, OrderType
from custom_api.dao import get_order_dao
from custom_api.database.connection import init_database, get_db_session, cleanup_database


def main():
    """主函数"""
    
    print("=== 订单模型使用示例 ===\n")
    
    try:
        # 1. 初始化数据库
        print("1. 初始化数据库...")
        init_database()
        print("   ✓ 数据库初始化成功\n")
        
        # 2. 获取DAO实例
        order_dao = get_order_dao()
        
        # 3. 创建示例订单
        print("2. 创建示例订单...")

        # 生成唯一标识符
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        unique_id = str(uuid.uuid4())[:8]

        # 黄金买入开仓
        gold_buy_order = {
            'order_no': f'ORD{timestamp}001',
            'execution_id': f'EXE{timestamp}001001_{unique_id}',
            'execution_time': datetime.now() - timedelta(hours=2),
            'channel_code': 'CHANNEL001',
            'symbol': 'XAUUSD',
            'side': SideType.BUY,
            'execution_price': Decimal('2650.50'),
            'execution_quantity': Decimal('1.0'),
            'effect_type': EffectType.OPEN,
            'order_type': OrderType.MARKET,
            'commission': Decimal('5.30'),
            'remark': '黄金买入开仓'
        }

        # 黄金卖出平仓
        gold_sell_order = {
            'order_no': f'ORD{timestamp}001',
            'execution_id': f'EXE{timestamp}001002_{unique_id}',
            'execution_time': datetime.now() - timedelta(hours=1),
            'channel_code': 'CHANNEL001',
            'symbol': 'XAUUSD',
            'side': SideType.SELL,
            'execution_price': Decimal('2655.80'),
            'execution_quantity': Decimal('1.0'),
            'effect_type': EffectType.CLOSE,
            'order_type': OrderType.MARKET,
            'commission': Decimal('5.30'),
            'remark': '黄金卖出平仓'
        }
        
        # 创建订单
        buy_order = order_dao.create(gold_buy_order)
        sell_order = order_dao.create(gold_sell_order)
        
        print(f"   ✓ 创建买入订单: {buy_order.order_no} - {buy_order.execution_id}")
        print(f"   ✓ 创建卖出订单: {sell_order.order_no} - {sell_order.execution_id}\n")
        
        # 4. 查询订单
        print("3. 查询订单...")
        
        # 根据订单号查询
        orders_by_no = order_dao.get_by_order_no(buy_order.order_no)
        print(f"   订单号 {buy_order.order_no} 的成交记录数: {len(orders_by_no)}")
        
        # 根据合约查询
        xauusd_orders = order_dao.get_by_symbol('XAUUSD')
        print(f"   XAUUSD 合约的订单数: {len(xauusd_orders)}")
        
        # 根据成交方向查询
        buy_orders = order_dao.get_by_side(SideType.BUY)
        print(f"   买入订单数: {len(buy_orders)}")
        
        sell_orders = order_dao.get_by_side(SideType.SELL)
        print(f"   卖出订单数: {len(sell_orders)}\n")
        
        # 5. 统计信息
        print("4. 统计信息...")
        stats = order_dao.get_statistics_by_symbol('XAUUSD')
        print(f"   XAUUSD 统计:")
        print(f"     总订单数: {stats['total_count']}")
        print(f"     买入订单数: {stats['buy_count']}")
        print(f"     卖出订单数: {stats['sell_count']}")
        print(f"     总成交量: {stats['total_volume']}")
        print(f"     总手续费: {stats['total_commission']}")
        print(f"     平均价格: {stats['avg_price']:.2f}")
        
        # 计算盈亏
        if stats['buy_count'] > 0 and stats['sell_count'] > 0:
            # 简单计算：假设买卖数量相等
            profit = (Decimal(str(stats['avg_price'])) - Decimal('2650.50')) * Decimal('1.0')
            print(f"     预估盈亏: {profit:.2f} USD\n")
        
        # 6. 复合搜索
        print("5. 复合搜索...")
        search_filters = {
            'symbol': 'XAUUSD',
            'start_date': datetime.now() - timedelta(days=1),
            'end_date': datetime.now()
        }
        
        search_results = order_dao.search_orders(search_filters)
        print(f"   搜索结果数量: {len(search_results)}")
        
        for order in search_results:
            print(f"     {order.execution_time.strftime('%H:%M:%S')} - "
                  f"{order.side.value} {order.execution_quantity} @ {order.execution_price}")
        
        print("\n=== 示例执行完成 ===")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理数据库资源
        cleanup_database()


if __name__ == "__main__":
    main()
